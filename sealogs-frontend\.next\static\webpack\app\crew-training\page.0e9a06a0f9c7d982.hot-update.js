"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\n// Status-based color classes for training titles\nconst getStatusColorClasses = (training)=>{\n    if (training.status.isOverdue) {\n        return \"text-destructive/80 hover:text-destructive\";\n    }\n    if (training.status.dueWithinSevenDays) {\n        return \"text-warning/80 hover:text-warning\";\n    }\n    return \"hover:text-curious-blue-400\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data } = param;\n    var _data_trainingType, _data_originalData_trainingTypes, _data_originalData, _data_vessel, _data_originalData1, _data_originalData2;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-2.5 tablet-md:border-none py-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", getStatusColorClasses(data)),\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 21\n                    }, undefined),\n                    !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.StatusBadge, {\n                        isOverdue: data.status.isOverdue,\n                        isUpcoming: data.status.dueWithinSevenDays,\n                        label: data.status.label || data.category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: isCompleted ? ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : (_data_originalData_trainingTypes = _data_originalData.trainingTypes) === null || _data_originalData_trainingTypes === void 0 ? void 0 : _data_originalData_trainingTypes.nodes) ? data.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : trainingTitle : trainingTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 115,\n                columnNumber: 17\n            }, undefined),\n            !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || ((_data_originalData1 = data.originalData) === null || _data_originalData1 === void 0 ? void 0 : _data_originalData1.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 128,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-lg\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp[\"tablet-md\"] ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 33\n                            }, undefined);\n                        }),\n                        members.length > (bp[\"tablet-md\"] ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp[\"tablet-md\"] ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp[\"tablet-md\"] ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 53\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 148,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && isCompleted && ((_data_originalData2 = data.originalData) === null || _data_originalData2 === void 0 ? void 0 : _data_originalData2.trainer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 225,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 84,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], unifiedData: preFilteredData, getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use pre-filtered data if available, otherwise merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (preFilteredData && Array.isArray(preFilteredData)) {\n            return preFilteredData;\n        }\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        preFilteredData,\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    // Create unified column structure for all training data types\n    const getUnifiedColumns = ()=>{\n        return [\n            // Mobile column - shows training card on mobile, adapts header based on data\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cellAlignment: \"left\",\n                cellClassName: \"w-ful xs:w-auto\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                        data: training\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 28\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    // Sort by category priority first, then by date\n                    const trainingA = rowA.original;\n                    const trainingB = rowB.original;\n                    const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                    const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                    if (priorityA !== priorityB) {\n                        return priorityA - priorityB;\n                    }\n                    const dateA = new Date(trainingA.dueDate).getTime();\n                    const dateB = new Date(trainingB.dueDate).getTime();\n                    return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n                }\n            },\n            // Training Type column - shows training types for all data types\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_trainingTypes, _training_originalData, _training_trainingType, _training_trainingType1;\n                    const training = row.original;\n                    const isCompleted = training.category === \"completed\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        children: isCompleted ? ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\" : ((_training_trainingType1 = training.trainingType) === null || _training_trainingType1 === void 0 ? void 0 : _training_trainingType1.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Vessel column - shows vessel information for all data types\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: isVesselView ? \"\" : \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel, _training_originalData;\n                    const training = row.original;\n                    if (isVesselView) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Crew column - shows crew members for all data types\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_members, _training_originalData, _training_status;\n                    const training = row.original;\n                    const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                    return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: members.map((member, index)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id || index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 33\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10 flex items-center justify-center text-sm font-medium\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                        children: members.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                    const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                    const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                    var _membersA__firstName, _membersA__surname;\n                    const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                    var _membersB__firstName, _membersB__surname;\n                    const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Trainer column - shows trainer for completed training, dash for others\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData;\n                    const training = row.original;\n                    const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                    if (!trainer || training.category !== \"completed\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    var _trainer_surname;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                    children: [\n                                        trainer.firstName,\n                                        \" \",\n                                        (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Status column - shows status badge at the end of the row\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.StatusBadge, {\n                        isOverdue: training.status.isOverdue,\n                        isUpcoming: training.status.dueWithinSevenDays,\n                        label: training.status.label || training.category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_status, _rowA_original, _rowB_original_status, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_status = _rowA_original.status) === null || _rowA_original_status === void 0 ? void 0 : _rowA_original_status.label) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_status = _rowB_original.status) === null || _rowB_original_status === void 0 ? void 0 : _rowB_original_status.label) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getUnifiedColumns()), [\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 587,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 594,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});