"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\n// Status-based color classes for training titles\nconst getStatusColorClasses = (training)=>{\n    if (training.status.isOverdue) {\n        return \"text-destructive/80 hover:text-destructive\";\n    }\n    if (training.status.dueWithinSevenDays) {\n        return \"text-warning/80 hover:text-warning\";\n    }\n    return \"hover:text-curious-blue-400\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data } = param;\n    var _data_trainingType, _data_originalData_trainingTypes, _data_originalData, _data_vessel, _data_originalData1, _data_originalData2;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-3 tablet-md:border-none py-3 small:pe-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", getStatusColorClasses(data)),\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 21\n                    }, undefined),\n                    !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.StatusBadge, {\n                        isOverdue: data.status.isOverdue,\n                        isUpcoming: data.status.dueWithinSevenDays,\n                        label: data.status.label || data.category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-[7px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Training/drill:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: isCompleted ? ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : (_data_originalData_trainingTypes = _data_originalData.trainingTypes) === null || _data_originalData_trainingTypes === void 0 ? void 0 : _data_originalData_trainingTypes.nodes) ? data.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : trainingTitle : trainingTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 115,\n                columnNumber: 17\n            }, undefined),\n            !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || ((_data_originalData1 = data.originalData) === null || _data_originalData1 === void 0 ? void 0 : _data_originalData1.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 133,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-lg\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp[\"tablet-md\"] ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 33\n                            }, undefined);\n                        }),\n                        members.length > (bp[\"tablet-md\"] ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp[\"tablet-md\"] ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp[\"tablet-md\"] ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 53\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 153,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && isCompleted && ((_data_originalData2 = data.originalData) === null || _data_originalData2 === void 0 ? void 0 : _data_originalData2.trainer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center pt-2 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 230,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 84,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], unifiedData: preFilteredData, getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use pre-filtered data if available, otherwise merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (preFilteredData && Array.isArray(preFilteredData)) {\n            return preFilteredData;\n        }\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        preFilteredData,\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    // Create unified column structure for all training data types\n    const getUnifiedColumns = ()=>{\n        return [\n            // Mobile column - shows training card on mobile, adapts header based on data\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cellAlignment: \"center\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                        data: training\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 28\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    // Sort by category priority first, then by date\n                    const trainingA = rowA.original;\n                    const trainingB = rowB.original;\n                    const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                    const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                    if (priorityA !== priorityB) {\n                        return priorityA - priorityB;\n                    }\n                    const dateA = new Date(trainingA.dueDate).getTime();\n                    const dateB = new Date(trainingB.dueDate).getTime();\n                    return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n                }\n            },\n            // Training Type column - shows training types for all data types\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_trainingTypes, _training_originalData, _training_trainingType, _training_trainingType1;\n                    const training = row.original;\n                    const isCompleted = training.category === \"completed\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        children: isCompleted ? ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\" : ((_training_trainingType1 = training.trainingType) === null || _training_trainingType1 === void 0 ? void 0 : _training_trainingType1.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Vessel column - shows vessel information for all data types\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: isVesselView ? \"\" : \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel, _training_originalData;\n                    const training = row.original;\n                    if (isVesselView) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Crew column - shows crew members for all data types\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_members, _training_originalData, _training_status;\n                    const training = row.original;\n                    const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                    return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: members.map((member, index)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id || index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 33\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10 flex items-center justify-center text-sm font-medium\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                        children: members.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                    const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                    const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                    var _membersA__firstName, _membersA__surname;\n                    const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                    var _membersB__firstName, _membersB__surname;\n                    const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Trainer column - shows trainer for completed training, dash for others\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData;\n                    const training = row.original;\n                    const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                    if (!trainer || training.category !== \"completed\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    var _trainer_surname;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                    children: [\n                                        trainer.firstName,\n                                        \" \",\n                                        (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getUnifiedColumns()), [\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 567,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 574,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});