// table.tsx
import * as React from 'react'
import { cn } from '@/app/lib/utils'

export const Table = React.forwardRef<
    HTMLTableElement,
    React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
    <div className={cn('relative w-full overflow-auto', className)}>
        <table
            ref={ref}
            cellSpacing={0}
            className="w-full caption-bottom border-spacing-0"
            {...props}
        />
    </div>
))
Table.displayName = 'Table'

export const TableHeader = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
    <thead
        ref={ref}
        className={cn('[&_tr]:border-border', className)}
        {...props}
    />
))
TableHeader.displayName = 'TableHeader'

export const TableBody = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, children, ...props }, ref) => (
    <tbody
        ref={ref}
        className={cn('[&_tr:last-child]:border-0', className)}
        {...props}>
        {children}
    </tbody>
))
TableBody.displayName = 'TableBody'

export const TableFooter = React.forwardRef<
    HTMLTableSectionElement,
    React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
    <tfoot
        ref={ref}
        className={cn(
            'border-t border-border bg-background/50 font-medium [&>tr]:last:border-b-0',
            className,
        )}
        {...props}
    />
))
TableFooter.displayName = 'TableFooter'

export const TableRow = React.forwardRef<
    HTMLTableRowElement,
    React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
    <tr
        ref={ref}
        className={cn(
            'relative cursor-pointer border-border group data-[state=selected]:bg-accent',
            className,
        )}
        {...props}>
        {props.children}
    </tr>
))
TableRow.displayName = 'TableRow'

export const TableHead = React.forwardRef<
    HTMLTableCellElement,
    React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
    <th
        ref={ref}
        className={cn(
            'h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative text-xs tiny:text-xs small:text-sm text-neutral-400 font-normal [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-fit whitespace-nowrap',
            className,
        )}
        {...props}
    />
))
TableHead.displayName = 'TableHead'

interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
    noHoverEffect?: boolean
    statusOverlay?: boolean
    statusOverlayColor?: 'destructive' | 'warning'
}

export const TableCell = React.forwardRef<HTMLTableCellElement, TableCellProps>(
    (
        {
            className,
            noHoverEffect = false,
            statusOverlay = false,
            statusOverlayColor,
            ...props
        },
        ref,
    ) => (
        <td
            ref={ref}
            className={cn(
                'h-20 font-normal align-center text-card-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5',
                className,
            )}
            {...props}>
            {/* Status overlay - only render on first cell and when statusOverlay is true */}
            {statusOverlay && (
                <span className="absolute flex w-full rounded-md pointer-events-none overflow-hidden inset-y-1 inset-x-0">
                    <span
                        className={cn(
                            'w-full rounded-md border bg-transparent',
                            // Only show on first cell
                            'hidden first:block',
                            statusOverlayColor === 'destructive' &&
                                'border-none bg-destructive/[2%]',
                            statusOverlayColor === 'warning' &&
                                'border-warning bg-warning/[2%]',
                        )}
                    />
                </span>
            )}

            {/* Only render hover effect on first cell using CSS :first-child selector */}
            {!noHoverEffect && (
                <span className="absolute flex w-full rounded-md pointer-events-none overflow-hidden inset-y-1 inset-x-0">
                    <span
                        className={cn(
                            'bg-accent',
                            'w-0', // start collapsed
                            'group-hover:w-full', // expand on row hover
                            'transition-[width] ease-out duration-300',
                            'will-change-transform will-change-width',
                            // Only show on first cell
                            'hidden first:block',
                            statusOverlayColor === 'destructive' &&
                                'm-px rounded-md bg-destructive/[3%]',
                            statusOverlayColor === 'warning' &&
                                'm-px rounded-md bg-warning/[3%]',
                        )}
                    />
                </span>
            )}

            <span className="relative flex flex-col w-full overflow-auto z-10">
                {props.children}
            </span>
        </td>
    ),
)
TableCell.displayName = 'TableCell'

export const TableCaption = React.forwardRef<
    HTMLTableCaptionElement,
    React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
    <caption
        ref={ref}
        className={cn('mt-4 text-muted-foreground', className)}
        {...props}
    />
))
TableCaption.displayName = 'TableCaption'
